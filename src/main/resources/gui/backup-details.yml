title: "&8Back<PERSON> gracza {player}"
rows: 6

items:
  info:
    material: PLAYER_HEAD
    displayname: "&#FFD700Informacje o Graczu"
    lore:
      - " &8» &fGracz: &#4ECDC4{player}"
      - " &8» &fP<PERSON><PERSON><PERSON><PERSON> śmierci: &#FF6B6B{death_cause}"
      - " &8» &fData śmierci: &#DDA0DD{death_date}"
      - " &8» &fKordynaty: &#45B7D1{death_location}"
    slot: 0
    action: NULL

  exp:
    material: EXPERIENCE_BOTTLE
    displayname: "&#00FF00Doświadczenie"
    lore:
      - " &8» &fPoziom: &#96CEB4{level}"
      - " &8» &fXP: &#00FF00{experience}"
    slot: 1
    action: NULL

  nadaj-backup:
    material: EMERALD
    displayname: "&#00FF00Nadaj Backup"
    lore:
      - " &8» &fStatus: {granted_status}"
      - " &8» &fNadany przez: &a{granted_by}"
      - ""
      - "&#00FF00Kliknij aby nadać backup!"
    slot: 2
    action: GRANT

  close:
    material: BARRIER
    displayname: "&#FF0000Zamknij"
    lore:
      - "&8» &#7F7F7FKliknij aby zamknąć menu"
    slot: 8
    action: CLOSE

  back:
    material: ARROW
    displayname: "&#FFD700Powrót"
    lore:
      - "&8» &#7F7F7FKliknij aby wrócić"
    slot: 3
    action: BACK

  helmet:
    slot: 9
    action: NULL
  chestplate:
    slot: 10
    action: NULL
  leggings:
    slot: 11
    action: NULL
  boots:
    slot: 12
    action: NULL
  offhand:
    slot: 13
    action: NULL

inventory-layout:
  main-inventory-slots:
    - 18
    - 19
    - 20
    - 21
    - 22
    - 23
    - 24
    - 25
    - 26
    - 27
    - 28
    - 29
    - 30
    - 31
    - 32
    - 33
    - 34
    - 35
    - 36
    - 37
    - 38
    - 39
    - 40
    - 41
    - 42
    - 43
    - 44

  hotbar-slots:
    - 45
    - 46
    - 47
    - 48
    - 49
    - 50
    - 51
    - 52
    - 53
