backup:
  granted: "&8» &#00FF00Backup został pomyślnie nadany!"
  claimed: "&8» &#00FF00Pomyślnie odebrałeś swój backup!"
  already-claimed: "&8» &#FF0000Ten backup został już odebrany!"
  already-granted: "&8» &#FF0000Ten backup został już nadany!"
  not-found: "&8» &#FF0000Nie znaleziono backupu!"
  no-backups-found: "&8» &#FF0000Nie znaleziono backupów dla gracza {player}!"
  no-granted-backups: "&8» &#FF0000Nie masz żadnych nadanych backupów do odebrania!"
  received-notification: "&8» &#00FF00Otr<PERSON><PERSON>łeś backup od {admin}! Użyj /odbierzbackupa aby go odebrać."
  empty-inventory-required: "&8» &#FF0000Aby odebrać backup, musisz mieć pusty ekwipunek!"

errors:
  no-permission: "&8» &#FF0000Nie masz uprawnień do użycia tej komendy!"
  player-not-found: "&8» &#FF0000Nie znaleziono gracza!"
  player-only: "&8» &#FF0000Ta komenda może być używana tylko przez graczy!"
  invalid-usage: "&8» &#FF0000Nieprawidłowe użycie komendy! &7Użyj: &f/nadajbackup <gracz>"
  database-error: "&8» &#FF0000Wystąpił błąd bazy danych. Spróbuj ponownie później."

status:
  granted-yes: "&aNadany"
  granted-no: "&cNie nadany"
  claimed-yes: "&a✔"
  claimed-no: "&4❌"
  granted-by-none: "Brak danych"