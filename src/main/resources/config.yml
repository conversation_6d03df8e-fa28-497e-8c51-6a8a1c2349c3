# <PERSON>za danych, do<PERSON><PERSON><PERSON>ie - FLAT
# Dostepne bazy danych: FLAT, MYSQL, MONGODB, H2
database:
  type: FLAT
  # MySQL: uri: "************************************************************"
  # MongoDB: uri: "mongodb://localhost:27017/backups"
  # H2: uri: "jdbc:h2:file:./plugins/777-Backupy/backups.db"
  uri: ""

# Ustawienia webhooka discord
discord:
  enabled: false
  webhook-url: ""
  embed:
    title: "Backup został nadany!"
    description: "> Gracz **{player}** otrzymał backupa"
    color: "#00FF00"
    timestamp: true
    footer: "777-Backupy"
    fields:
      player:
        name: "`👤` Gracz"
        value: "{player}"
        inline: true
      granted_by:
        name: "`🛡️` Nadane przez"
        value: "{granted_by}"
        inline: true
      death_cause:
        name: "`💀` <PERSON><PERSON><PERSON><PERSON>yna śmierci"
        value: "{death_cause}"
        inline: true
      death_date:
        name: "`📅` Data śmierci"
        value: "{death_date}"
        inline: true
      location:
        name: "`📍` Kordy"
        value: "{death_location}"
        inline: true
      level_exp:
        name: "`⭐` Poziom XP"
        value: "Poziom: {level} | XP: {experience}"
        inline: true
