package me.darkness.backup.configuration;

import me.darkness.backup.Main;
import me.darkness.backup.utils.ColorUtils;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

public class Messages {

    private final Main plugin;
    private File messagesFile;
    private FileConfiguration messagesConfig;

    public Messages(Main plugin) {
        this.plugin = plugin;
    }

    public void loadConfiguration() {
        messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
        
        InputStream defaultStream = plugin.getResource("messages.yml");
        if (defaultStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defaultStream));
            messagesConfig.setDefaults(defaultConfig);
        }
    }

    public void reloadConfiguration() {
        messagesConfig = YamlConfiguration.loadConfiguration(messagesFile);
        
        InputStream defaultStream = plugin.getResource("messages.yml");
        if (defaultStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defaultStream));
            messagesConfig.setDefaults(defaultConfig);
        }
    }

    public void saveConfiguration() {
        try {
            messagesConfig.save(messagesFile);
        } catch (IOException ignored) {}
    }

    public String getMessage(String path) {
        return ColorUtils.colorize(messagesConfig.getString(path, "Message not found: " + path));
    }

    public String getMessage(String path, String... placeholders) {
        String message = getMessage(path);
        
        for (int i = 0; i < placeholders.length; i += 2) {
            if (i + 1 < placeholders.length) {
                message = message.replace(placeholders[i], placeholders[i + 1]);
            }
        }
        
        return message;
    }

    public String getBackupGrantedMessage() {
        return getMessage("backup.granted");
    }

    public String getBackupClaimedMessage() {
        return getMessage("backup.claimed");
    }

    public String getBackupAlreadyClaimedMessage() {
        return getMessage("backup.already-claimed");
    }

    public String getBackupNotFoundMessage() {
        return getMessage("backup.not-found");
    }

    public String getNoPermissionMessage() {
        return getMessage("errors.no-permission");
    }

    public String getPlayerNotFoundMessage() {
        return getMessage("errors.player-not-found");
    }

    public String getInvalidUsageMessage() {
        return getMessage("errors.invalid-usage");
    }

    public FileConfiguration getConfig() {
        return messagesConfig;
    }
}
