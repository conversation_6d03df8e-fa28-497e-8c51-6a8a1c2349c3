package me.darkness.backup.configuration;

import me.darkness.backup.Main;
import org.bukkit.configuration.file.FileConfiguration;

public class Configuration {

    private final Main plugin;
    private FileConfiguration config;

    public Configuration(Main plugin) {
        this.plugin = plugin;
    }

    public void loadConfiguration() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        config = plugin.getConfig();
    }

    public void reloadConfiguration() {
        plugin.reloadConfig();
        config = plugin.getConfig();
    }

    public String getDatabaseType() {
        return config.getString("database.type", "FLAT");
    }

    public String getDatabaseUri() {
        return config.getString("database.uri", "");
    }

    public String getDiscordWebhookUrl() {
        return config.getString("discord.webhook-url", "");
    }

    public boolean isDiscordEnabled() {
        return config.getBoolean("discord.enabled", false) && !getDiscordWebhookUrl().isEmpty();
    }

    public String getDiscordEmbedTitle() {
        return config.getString("discord.embed.title", "Backup Granted");
    }

    public String getDiscordEmbedDescription() {
        return config.getString("discord.embed.description", "A backup has been granted to a player.");
    }

    public String getDiscordEmbedColor() {
        return config.getString("discord.embed.color", "#00FF00");
    }

    public String getDiscordEmbedFooter() {
        return config.getString("discord.embed.footer", "777-Backup System");
    }

    public boolean getDiscordEmbedTimestamp() {
        return config.getBoolean("discord.embed.timestamp", true);
    }

    public boolean isDiscordEmbedThumbnailEnabled() {
        return true;
    }

    public String getDiscordEmbedThumbnail() {
        return "https://mc-heads.net/avatar/{player}/256";
    }

    public String getDiscordEmbedAuthorName() {
        return config.getString("discord.embed.author.name", "");
    }

    public String getDiscordEmbedAuthorIconUrl() {
        return config.getString("discord.embed.author.icon_url", "");
    }

    public org.bukkit.configuration.ConfigurationSection getDiscordEmbedFields() {
        return config.getConfigurationSection("discord.embed.fields");
    }

    public FileConfiguration getConfig() {
        return config;
    }
}
