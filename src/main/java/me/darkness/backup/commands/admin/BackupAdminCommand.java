package me.darkness.backup.commands.admin;

import me.darkness.backup.Main;
import me.darkness.backup.models.BackupData;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class BackupAdminCommand implements CommandExecutor {

    private final Main plugin;

    public BackupAdminCommand(Main plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(plugin.getMessages().getMessage("errors.player-only"));
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("backup.admin")) {
            player.sendMessage(plugin.getMessages().getNoPermissionMessage());
            return true;
        }

        if (args.length != 1) {
            player.sendMessage(plugin.getMessages().getInvalidUsageMessage());
            return true;
        }

        String targetPlayerName = args[0];
        Player targetPlayer = Bukkit.getPlayer(targetPlayerName);

        if (targetPlayer != null) {
            openBackupGui(player, targetPlayer.getUniqueId(), targetPlayer.getName());
        } else {
            plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                @SuppressWarnings("deprecation")
                org.bukkit.OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(targetPlayerName);

                if (offlinePlayer.hasPlayedBefore()) {
                    openBackupGui(player, offlinePlayer.getUniqueId(), offlinePlayer.getName());
                } else {
                    player.sendMessage(plugin.getMessages().getPlayerNotFoundMessage());
                }
            });
        }

        return true;
    }

    private void openBackupGui(Player admin, java.util.UUID targetUuid, String targetName) {
        plugin.getBackupManager().getPlayerBackups(targetUuid).thenAccept(backups -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (backups.isEmpty()) {
                    admin.sendMessage(plugin.getMessages().getMessage("backup.no-backups-found",
                            "{player}", targetName));
                    return;
                }

                plugin.getGuiManager().openBackupListGui(admin, backups, targetName);
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                admin.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });
    }
}
