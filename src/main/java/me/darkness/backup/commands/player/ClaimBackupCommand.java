package me.darkness.backup.commands.player;

import me.darkness.backup.Main;
import me.darkness.backup.models.BackupData;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.List;

public class ClaimBackupCommand implements CommandExecutor {

    private final Main plugin;

    public ClaimBackupCommand(Main plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(plugin.getMessages().getMessage("errors.player-only"));
            return true;
        }

        Player player = (Player) sender;

        if (!isInventoryEmpty(player)) {
            player.sendMessage(plugin.getMessages().getMessage("backup.empty-inventory-required"));
            return true;
        }
        plugin.getBackupManager().getGrantedBackups(player.getUniqueId()).thenAccept(grantedBackups -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (grantedBackups.isEmpty()) {
                    player.sendMessage(plugin.getMessages().getMessage("backup.no-granted-backups"));
                    return;
                }

                if (grantedBackups.size() == 1) {
                    claimBackup(player, grantedBackups.get(0).getId());
                } else {
                    plugin.getGuiManager().openClaimBackupGui(player, grantedBackups);
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });

        return true;
    }

    private void claimBackup(Player player, String backupId) {
        plugin.getBackupManager().claimBackup(backupId, player).thenAccept(success -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (success) {
                    player.sendMessage(plugin.getMessages().getBackupClaimedMessage());
                } else {
                    player.sendMessage(plugin.getMessages().getBackupNotFoundMessage());
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });
    }

    private boolean isInventoryEmpty(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && !item.getType().isAir()) {
                return false;
            }
        }

        for (ItemStack item : player.getInventory().getArmorContents()) {
            if (item != null && !item.getType().isAir()) {
                return false;
            }
        }

        try {
            ItemStack offHand = player.getInventory().getItemInOffHand();
            if (offHand != null && !offHand.getType().isAir()) {
                return false;
            }
        } catch (NoSuchMethodError ignored) {}

        return true;
    }
}
