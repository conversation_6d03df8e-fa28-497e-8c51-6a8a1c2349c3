package me.darkness.backup.gui;

import me.darkness.backup.Main;
import me.darkness.backup.gui.guis.BackupDetailsGui;
import me.darkness.backup.gui.guis.BackupListGui;
import me.darkness.backup.gui.guis.ClaimBackupGui;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;

import java.util.List;

public class GuiManager {

    private final Main plugin;
    private final GuiConfiguration guiConfigManager;

    public GuiManager(Main plugin) {
        this.plugin = plugin;
        this.guiConfigManager = new GuiConfiguration(plugin);

        plugin.getServer().getPluginManager().registerEvents(new GuiListener(plugin), plugin);
    }

    public void openBackupListGui(Player player, List<BackupData> backups, String targetPlayerName) {
        BackupListGui gui = new BackupListGui(plugin, guiConfigManager);
        gui.open(player, backups, targetPlayerName);
    }

    public void openBackupDetailsGui(Player player, BackupData backup) {
        BackupDetailsGui gui = new BackupDetailsGui(plugin, guiConfigManager);
        gui.open(player, backup);
    }

    public void openClaimBackupGui(Player player, List<BackupData> grantedBackups) {
        ClaimBackupGui gui = new ClaimBackupGui(plugin, guiConfigManager);
        gui.open(player, grantedBackups);
    }

    public GuiConfiguration getGuiConfigManager() {
        return guiConfigManager;
    }
}
