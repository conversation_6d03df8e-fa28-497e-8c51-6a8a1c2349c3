package me.darkness.backup.gui;

import me.darkness.backup.Main;
import me.darkness.backup.gui.guis.BaseGui;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.InventoryHolder;

public class GuiListener implements Listener {

    private final Main plugin;

    public GuiListener(Main plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        InventoryHolder holder = event.getInventory().getHolder();
        if (!(holder instanceof BaseGui)) {
            return;
        }

        event.setCancelled(true);

        BaseGui gui = (BaseGui) holder;
        Player player = (Player) event.getWhoClicked();

        gui.handleClick(player, event.getSlot(), event.getClick());
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        InventoryHolder holder = event.getInventory().getHolder();
        if (!(holder instanceof BaseGui)) {
            return;
        }

        BaseGui gui = (BaseGui) holder;
        Player player = (Player) event.getPlayer();

        gui.handleClose(player);
    }
}
