package me.darkness.backup.gui;

import me.darkness.backup.Main;
import me.darkness.backup.utils.ColorUtils;
import org.bukkit.Material;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.stream.Collectors;

public class GuiConfiguration {

    private final Main plugin;
    private final File guiFolder;

    public GuiConfiguration(Main plugin) {
        this.plugin = plugin;
        this.guiFolder = new File(plugin.getDataFolder(), "gui");
        
        initializeGuiConfigurations();
    }

    private void initializeGuiConfigurations() {
        if (!guiFolder.exists()) {
            guiFolder.mkdirs();
        }
        
        createDefaultGuiConfig("backup-list.yml");
        createDefaultGuiConfig("backup-details.yml");
        createDefaultGuiConfig("claim-backup.yml");
    }

    private void createDefaultGuiConfig(String fileName) {
        File configFile = new File(guiFolder, fileName);
        if (!configFile.exists()) {
            plugin.saveResource("gui/" + fileName, false);
        }
    }

    public FileConfiguration getGuiConfig(String guiName) {
        File configFile = new File(guiFolder, guiName + ".yml");
        
        if (!configFile.exists()) {
            return new YamlConfiguration();
        }
        
        FileConfiguration config = YamlConfiguration.loadConfiguration(configFile);
        
        InputStream defaultStream = plugin.getResource("gui/" + guiName + ".yml");
        if (defaultStream != null) {
            YamlConfiguration defaultConfig = YamlConfiguration.loadConfiguration(new InputStreamReader(defaultStream));
            config.setDefaults(defaultConfig);
        }
        
        return config;
    }

    public String getGuiTitle(String guiName) {
        FileConfiguration config = getGuiConfig(guiName);
        return ColorUtils.colorize(config.getString("title", "Backup GUI"));
    }

    public int getGuiRows(String guiName) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getInt("rows", 6);
    }

    public Material getItemMaterial(String guiName, String itemPath) {
        FileConfiguration config = getGuiConfig(guiName);
        String materialName = config.getString("items." + itemPath + ".material", "PAPER");

        try {
            return Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            return Material.PAPER;
        }
    }

    public String getItemDisplayName(String guiName, String itemPath) {
        FileConfiguration config = getGuiConfig(guiName);
        return ColorUtils.colorize(config.getString("items." + itemPath + ".displayname", ""));
    }

    public List<String> getItemLore(String guiName, String itemPath) {
        FileConfiguration config = getGuiConfig(guiName);
        List<String> lore = config.getStringList("items." + itemPath + ".lore");
        
        return lore.stream()
                .map(ColorUtils::colorize)
                .collect(Collectors.toList());
    }

    public int getItemSlot(String guiName, String itemPath) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getInt("items." + itemPath + ".slot", 0);
    }

    public String getItemAction(String guiName, String itemPath) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getString("items." + itemPath + ".action", "NULL");
    }

    public List<Integer> getInventorySlots(String guiName, String layoutName) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getIntegerList(layoutName + ".main-inventory-slots");
    }

    public List<Integer> getHotbarSlots(String guiName, String layoutName) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getIntegerList(layoutName + ".hotbar-slots");
    }

    public List<Integer> getBackupSlots(String guiName, String layoutName) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getIntegerList(layoutName + ".backup-slots");
    }

    public List<Integer> getGrantedBackupSlots(String guiName, String layoutName) {
        FileConfiguration config = getGuiConfig(guiName);
        return config.getIntegerList(layoutName + ".granted-backup-slots");
    }
}
