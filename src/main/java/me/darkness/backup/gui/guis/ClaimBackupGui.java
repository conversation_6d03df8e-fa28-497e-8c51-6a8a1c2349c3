package me.darkness.backup.gui.guis;

import me.darkness.backup.Main;
import me.darkness.backup.gui.GuiConfiguration;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ClaimBackupGui extends BaseGui {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    
    private List<BackupData> grantedBackups;
    private Map<Integer, BackupData> slotToBackupMap;

    public ClaimBackupGui(Main plugin, GuiConfiguration guiConfigManager) {
        super(plugin, guiConfigManager, "claim-backup");
        this.slotToBackupMap = new HashMap<>();
    }

    public void open(Player player, List<BackupData> grantedBackups) {
        this.grantedBackups = grantedBackups;
        
        createInventory();
        populateInventory();
        
        player.openInventory(inventory);
    }

    private void populateInventory() {
        slotToBackupMap.clear();
        inventory.clear();
        setItem("close");

        List<Integer> grantedBackupSlots = guiConfigManager.getGrantedBackupSlots(guiName, "granted-backup-layout");

        for (int i = 0; i < grantedBackups.size() && i < grantedBackupSlots.size(); i++) {
            BackupData backup = grantedBackups.get(i);
            int slot = grantedBackupSlots.get(i);

            ItemStack backupItem = createGrantedBackupItem(backup);
            inventory.setItem(slot, backupItem);
            slotToBackupMap.put(slot, backup);
        }
    }

    private ItemStack createGrantedBackupItem(BackupData backup) {
        String grantedBy = backup.getGrantedByName() != null ? backup.getGrantedByName() : "Nieznany";
        String grantedDate = backup.getGrantedTime() != null ? 
                backup.getGrantedTime().format(DATE_FORMATTER) : "Nieznana";
        
        return createItem("granted-backup-item",
                "{death_cause}", backup.getDeathCause(),
                "{death_date}", backup.getDeathTime().format(DATE_FORMATTER),
                "{death_location}", backup.getDeathLocation(),
                "{granted_by}", grantedBy,
                "{granted_date}", grantedDate,
                "{level}", String.valueOf(backup.getLevel()),
                "{experience}", String.valueOf(backup.getExperience())
        );
    }

    @Override
    public void handleClick(Player player, int slot, ClickType clickType) {
        org.bukkit.configuration.file.FileConfiguration config = guiConfigManager.getGuiConfig(guiName);
        org.bukkit.configuration.ConfigurationSection itemsSection = config.getConfigurationSection("items");

        if (itemsSection != null) {
            for (String itemKey : itemsSection.getKeys(false)) {
                int itemSlot = guiConfigManager.getItemSlot(guiName, itemKey);
                if (slot == itemSlot && !itemKey.equals("granted-backup-item")) {
                    String action = guiConfigManager.getItemAction(guiName, itemKey);

                    switch (action.toUpperCase()) {
                        case "CLOSE":
                            executeAction(player, action);
                            return;
                        case "CLAIM":
                            BackupData backup = slotToBackupMap.get(slot);
                            if (backup != null) {
                                claimBackup(player, backup);
                            }
                            return;
                    }
                }
            }
        }

        BackupData backup = slotToBackupMap.get(slot);
        if (backup != null) {
            claimBackup(player, backup);
        }
    }

    private void claimBackup(Player player, BackupData backup) {
        if (!isInventoryEmpty(player)) {
            player.sendMessage(plugin.getMessages().getMessage("backup.empty-inventory-required"));
            return;
        }

        plugin.getBackupManager().claimBackup(backup.getId(), player).thenAccept(success -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (success) {
                    player.sendMessage(plugin.getMessages().getBackupClaimedMessage());

                    grantedBackups.remove(backup);
                    if (grantedBackups.isEmpty()) {
                        player.closeInventory();
                        player.sendMessage(plugin.getMessages().getMessage("backup.no-granted-backups"));
                    } else {
                        populateInventory();
                    }
                } else {
                    player.sendMessage(plugin.getMessages().getBackupAlreadyClaimedMessage());
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                player.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });
    }

    private boolean isInventoryEmpty(Player player) {
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && !item.getType().isAir()) {
                return false;
            }
        }

        for (ItemStack item : player.getInventory().getArmorContents()) {
            if (item != null && !item.getType().isAir()) {
                return false;
            }
        }

        try {
            ItemStack offHand = player.getInventory().getItemInOffHand();
            if (offHand != null && !offHand.getType().isAir()) {
                return false;
            }
        } catch (NoSuchMethodError ignored) {}

        return true;
    }
}
