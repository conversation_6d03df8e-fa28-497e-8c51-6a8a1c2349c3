package me.darkness.backup.gui.guis;

import me.darkness.backup.Main;
import me.darkness.backup.gui.GuiConfiguration;
import me.darkness.backup.utils.ColorUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;
import java.util.stream.Collectors;

public abstract class BaseGui implements InventoryHolder {

    protected final Main plugin;
    protected final GuiConfiguration guiConfigManager;
    protected final String guiName;
    protected Inventory inventory;

    public BaseGui(Main plugin, GuiConfiguration guiConfigManager, String guiName) {
        this.plugin = plugin;
        this.guiConfigManager = guiConfigManager;
        this.guiName = guiName;
    }

    protected void createInventory() {
        createInventory(new String[0]);
    }

    protected void createInventory(String... placeholders) {
        String title = replacePlaceholders(guiConfigManager.getGuiTitle(guiName), placeholders);
        int rows = guiConfigManager.getGuiRows(guiName);
        inventory = Bukkit.createInventory(this, rows * 9, title);
    }

    protected ItemStack createItem(String itemPath) {
        return createItem(itemPath, new String[0]);
    }

    protected ItemStack createItem(String itemPath, String... placeholders) {
        Material material = guiConfigManager.getItemMaterial(guiName, itemPath);
        String displayName = guiConfigManager.getItemDisplayName(guiName, itemPath);
        List<String> lore = guiConfigManager.getItemLore(guiName, itemPath);

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            if (!displayName.isEmpty()) {
                String processedDisplayName = replacePlaceholders(displayName, placeholders);
                meta.setDisplayName(processedDisplayName);
            }

            if (!lore.isEmpty()) {
                List<String> processedLore = lore.stream()
                        .map(line -> replacePlaceholders(line, placeholders))
                        .collect(Collectors.toList());
                meta.setLore(processedLore);
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    protected void setItem(String itemPath, String... placeholders) {
        int slot = guiConfigManager.getItemSlot(guiName, itemPath);
        ItemStack item = createItem(itemPath, placeholders);
        inventory.setItem(slot, item);
    }

    protected String replacePlaceholders(String text, String... placeholders) {
        String result = text;
        
        for (int i = 0; i < placeholders.length; i += 2) {
            if (i + 1 < placeholders.length) {
                result = result.replace(placeholders[i], placeholders[i + 1]);
            }
        }
        
        return result;
    }

    protected void executeAction(Player player, String action, Object... data) {
        switch (action.toUpperCase()) {
            case "CLOSE":
                player.closeInventory();
                break;
            case "BACK":
                handleBackAction(player, data);
                break;
        }
    }

    protected void handleBackAction(Player player, Object... data) {
        player.closeInventory();
    }

    public abstract void handleClick(Player player, int slot, ClickType clickType);

    public void handleClose(Player player) {}

    @Override
    public Inventory getInventory() {
        return inventory;
    }
}
