package me.darkness.backup.gui.guis;

import me.darkness.backup.Main;
import me.darkness.backup.gui.GuiConfiguration;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;

import java.time.format.DateTimeFormatter;
import java.util.List;

public class BackupDetailsGui extends BaseGui {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    
    private BackupData backup;

    public BackupDetailsGui(Main plugin, GuiConfiguration guiConfigManager) {
        super(plugin, guiConfigManager, "backup-details");
    }

    public void open(Player player, BackupData backup) {
        this.backup = backup;

        createInventory("{player}", backup.getPlayerName());
        populateInventory();

        player.openInventory(inventory);
    }

    private void populateInventory() {
        displayPlayerInventory();
        displayPlayerArmor();
        displayOffHandItem();

        org.bukkit.configuration.file.FileConfiguration config = guiConfigManager.getGuiConfig(guiName);
        org.bukkit.configuration.ConfigurationSection itemsSection = config.getConfigurationSection("items");

        if (itemsSection != null) {
            for (String itemKey : itemsSection.getKeys(false)) {
                if (itemKey.equals("helmet") || itemKey.equals("chestplate") ||
                    itemKey.equals("leggings") || itemKey.equals("boots") || itemKey.equals("offhand")) {
                    continue;
                }

                if (itemKey.contains("player") || itemKey.contains("info")) {
                    setItem(itemKey,
                            "{player}", backup.getPlayerName(),
                            "{death_cause}", backup.getDeathCause(),
                            "{death_date}", backup.getDeathTime().format(DATE_FORMATTER),
                            "{death_location}", backup.getDeathLocation()
                    );
                } else if (itemKey.contains("experience") || itemKey.contains("exp") || itemKey.contains("bottle")) {
                    float expProgress = backup.getExperienceProgress() * 100;
                    setItem(itemKey,
                            "{level}", String.valueOf(backup.getLevel()),
                            "{experience}", String.valueOf(backup.getExperience()),
                            "{experience_progress}", String.format("%.1f", expProgress)
                    );
                } else if (itemKey.contains("grant") || itemKey.contains("backup")) {
                    String grantedStatus = backup.getGrantedBy() != null ?
                        plugin.getMessages().getMessage("status.granted-yes") :
                        plugin.getMessages().getMessage("status.granted-no");
                    String grantedBy = backup.getGrantedByName() != null ? backup.getGrantedByName() : plugin.getMessages().getMessage("status.granted-by-none");
                    setItem(itemKey,
                            "{granted_status}", grantedStatus,
                            "{granted_by}", grantedBy
                    );
                } else {
                    setItem(itemKey);
                }
            }
        }
    }

    private void displayPlayerInventory() {
        ItemStack[] inventory = backup.getInventory();
        if (inventory == null) return;

        List<Integer> mainInventorySlots = guiConfigManager.getInventorySlots(guiName, "inventory-layout");

        int slotIndex = 0;
        for (int i = 9; i < 36 && i < inventory.length && slotIndex < mainInventorySlots.size(); i++) {
            ItemStack item = inventory[i];
            if (item != null) {
                this.inventory.setItem(mainInventorySlots.get(slotIndex), item);
            }
            slotIndex++;
        }

        List<Integer> hotbarSlots = guiConfigManager.getHotbarSlots(guiName, "inventory-layout");

        for (int i = 0; i < Math.min(hotbarSlots.size(), 9) && i < inventory.length; i++) {
            ItemStack item = inventory[i];
            if (item != null) {
                this.inventory.setItem(hotbarSlots.get(i), item);
            }
        }
    }

    private void displayPlayerArmor() {
        ItemStack[] armor = backup.getArmor();
        if (armor == null) return;

        org.bukkit.configuration.file.FileConfiguration config = guiConfigManager.getGuiConfig(guiName);
        org.bukkit.configuration.ConfigurationSection itemsSection = config.getConfigurationSection("items");

        if (itemsSection != null) {
            for (String itemKey : itemsSection.getKeys(false)) {
                try {
                    int slot = guiConfigManager.getItemSlot(guiName, itemKey);

                    if (itemKey.contains("helmet") && armor.length > 3 && armor[3] != null) {
                        this.inventory.setItem(slot, armor[3]);
                    } else if (itemKey.contains("chestplate") && armor.length > 2 && armor[2] != null) {
                        this.inventory.setItem(slot, armor[2]);
                    } else if (itemKey.contains("leggings") && armor.length > 1 && armor[1] != null) {
                        this.inventory.setItem(slot, armor[1]);
                    } else if (itemKey.contains("boots") && armor.length > 0 && armor[0] != null) {
                        this.inventory.setItem(slot, armor[0]);
                    }
                } catch (Exception ignored) {}
            }
        }
    }

    private void displayOffHandItem() {
        ItemStack offHand = backup.getOffHand();
        if (offHand == null) return;

        org.bukkit.configuration.file.FileConfiguration config = guiConfigManager.getGuiConfig(guiName);
        org.bukkit.configuration.ConfigurationSection itemsSection = config.getConfigurationSection("items");

        if (itemsSection != null) {
            for (String itemKey : itemsSection.getKeys(false)) {
                if (itemKey.contains("offhand") || itemKey.contains("off-hand")) {
                    try {
                        int slot = guiConfigManager.getItemSlot(guiName, itemKey);
                        this.inventory.setItem(slot, offHand);
                        break;
                    } catch (Exception ignored) {}
                }
            }
        }
    }

    @Override
    public void handleClick(Player player, int slot, ClickType clickType) {
        org.bukkit.configuration.file.FileConfiguration config = guiConfigManager.getGuiConfig(guiName);
        org.bukkit.configuration.ConfigurationSection itemsSection = config.getConfigurationSection("items");

        if (itemsSection != null) {
            for (String itemKey : itemsSection.getKeys(false)) {
                int itemSlot = guiConfigManager.getItemSlot(guiName, itemKey);
                if (slot == itemSlot) {
                    String action = guiConfigManager.getItemAction(guiName, itemKey);

                    switch (action.toUpperCase()) {
                        case "GRANT":
                            if (backup.getGrantedBy() == null) {
                                grantBackup(player, backup);
                            } else {
                                player.sendMessage(plugin.getMessages().getMessage("backup.already-granted"));
                            }
                            break;
                        case "CLOSE":
                            executeAction(player, action);
                            break;
                        case "BACK":
                            executeAction(player, action, backup);
                            break;
                        case "NULL":
                        default:
                            break;
                    }
                    return;
                }
            }
        }
    }

    private void grantBackup(Player admin, BackupData backup) {
        plugin.getBackupManager().grantBackup(backup.getId(), admin).thenRun(() -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                admin.sendMessage(plugin.getMessages().getBackupGrantedMessage());

                backup.setGrantedBy(admin.getUniqueId());
                backup.setGrantedByName(admin.getName());
                backup.setGrantedTime(java.time.LocalDateTime.now());

                populateInventory();

                Player targetPlayer = plugin.getServer().getPlayer(backup.getPlayerUuid());
                if (targetPlayer != null) {
                    targetPlayer.sendMessage(plugin.getMessages().getMessage("backup.received-notification",
                            "{admin}", admin.getName()));
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                admin.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });
    }

    @Override
    protected void handleBackAction(Player player, Object... data) {
        if (data.length > 0 && data[0] instanceof BackupData) {
            BackupData backup = (BackupData) data[0];
            plugin.getBackupManager().getPlayerBackups(backup.getPlayerUuid()).thenAccept(backups -> {
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    plugin.getGuiManager().openBackupListGui(player, backups, backup.getPlayerName());
                });
            });
        } else {
            super.handleBackAction(player, data);
        }
    }
}
