package me.darkness.backup.gui.guis;

import me.darkness.backup.Main;
import me.darkness.backup.gui.GuiConfiguration;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BackupListGui extends BaseGui {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
    
    private List<BackupData> backups;
    private String targetPlayerName;
    private Map<Integer, BackupData> slotToBackupMap;

    public BackupListGui(Main plugin, GuiConfiguration guiConfigManager) {
        super(plugin, guiConfigManager, "backup-list");
        this.slotToBackupMap = new HashMap<>();
    }

    public void open(Player player, List<BackupData> backups, String targetPlayerName) {
        this.backups = backups;
        this.targetPlayerName = targetPlayerName;

        createInventory("{player}", targetPlayerName);
        populateInventory();

        player.openInventory(inventory);
    }

    private void populateInventory() {
        slotToBackupMap.clear();
        inventory.clear();
        setItem("close");

        List<Integer> backupSlots = guiConfigManager.getBackupSlots(guiName, "backup-layout");

        for (int i = 0; i < backups.size() && i < backupSlots.size(); i++) {
            BackupData backup = backups.get(i);
            int slot = backupSlots.get(i);

            ItemStack backupItem = createBackupItem(backup);
            inventory.setItem(slot, backupItem);
            slotToBackupMap.put(slot, backup);
        }
    }

    private ItemStack createBackupItem(BackupData backup) {
        String grantedStatus = backup.getGrantedBy() != null ?
            plugin.getMessages().getMessage("status.granted-yes") :
            plugin.getMessages().getMessage("status.granted-no");
        String claimedStatus = backup.isClaimed() ?
            plugin.getMessages().getMessage("status.claimed-yes") :
            plugin.getMessages().getMessage("status.claimed-no");
        String grantedBy = backup.getGrantedByName() != null ? backup.getGrantedByName() : plugin.getMessages().getMessage("status.granted-by-none");

        return createItem("backup-item",
                "{player}", targetPlayerName,
                "{death_cause}", backup.getDeathCause(),
                "{death_date}", backup.getDeathTime().format(DATE_FORMATTER),
                "{death_location}", backup.getDeathLocation(),
                "{granted_status}", grantedStatus,
                "{claimed_status}", claimedStatus,
                "{granted_by}", grantedBy,
                "{level}", String.valueOf(backup.getLevel()),
                "{experience}", String.valueOf(backup.getExperience())
        );
    }

    @Override
    public void handleClick(Player player, int slot, ClickType clickType) {
        if (slot == guiConfigManager.getItemSlot(guiName, "close")) {
            executeAction(player, guiConfigManager.getItemAction(guiName, "close"));
            return;
        }

        BackupData backup = slotToBackupMap.get(slot);
        if (backup != null) {
            if (clickType == ClickType.RIGHT) {
                plugin.getGuiManager().openBackupDetailsGui(player, backup);
            } else if (clickType == ClickType.LEFT) {
                if (backup.getGrantedBy() == null) {
                    grantBackup(player, backup);
                } else {
                    player.sendMessage(plugin.getMessages().getMessage("backup.already-granted"));
                }
            }
        }
    }

    private void grantBackup(Player admin, BackupData backup) {
        plugin.getBackupManager().grantBackup(backup.getId(), admin).thenRun(() -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                admin.sendMessage(plugin.getMessages().getBackupGrantedMessage());
                populateInventory();

                Player targetPlayer = plugin.getServer().getPlayer(backup.getPlayerUuid());
                if (targetPlayer != null) {
                    targetPlayer.sendMessage(plugin.getMessages().getMessage("backup.received-notification",
                            "{admin}", admin.getName()));
                }
            });
        }).exceptionally(throwable -> {
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                admin.sendMessage(plugin.getMessages().getMessage("errors.database-error"));
            });
            return null;
        });
    }
}
