package me.darkness.backup.models;

import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;
import java.util.UUID;

public class BackupData {

    private String id;
    private UUID playerUuid;
    private String playerName;
    private ItemStack[] inventory;
    private ItemStack[] armor;
    private ItemStack offHand;
    private int experience;
    private int level;
    private float experienceProgress;
    private String deathCause;
    private LocalDateTime deathTime;
    private String deathLocation;
    private String killer;
    private boolean claimed;
    private UUID grantedBy;
    private String grantedByName;
    private LocalDateTime grantedTime;

    public BackupData() {
    }

    public BackupData(UUID playerUuid, String playerName, ItemStack[] inventory, ItemStack[] armor,
                     ItemStack offHand, int experience, int level, float experienceProgress,
                     String deathCause, LocalDateTime deathTime, String deathLocation) {
        this.id = UUID.randomUUID().toString();
        this.playerUuid = playerUuid;
        this.playerName = playerName;
        this.inventory = inventory;
        this.armor = armor;
        this.offHand = offHand;
        this.experience = experience;
        this.level = level;
        this.experienceProgress = experienceProgress;
        this.deathCause = deathCause;
        this.deathTime = deathTime;
        this.deathLocation = deathLocation;
        this.claimed = false;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public UUID getPlayerUuid() {
        return playerUuid;
    }

    public void setPlayerUuid(UUID playerUuid) {
        this.playerUuid = playerUuid;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public ItemStack[] getInventory() {
        return inventory;
    }

    public void setInventory(ItemStack[] inventory) {
        this.inventory = inventory;
    }

    public ItemStack[] getArmor() {
        return armor;
    }

    public void setArmor(ItemStack[] armor) {
        this.armor = armor;
    }

    public ItemStack getOffHand() {
        return offHand;
    }

    public void setOffHand(ItemStack offHand) {
        this.offHand = offHand;
    }

    public int getExperience() {
        return experience;
    }

    public void setExperience(int experience) {
        this.experience = experience;
    }

    public int getLevel() {
        return level;
    }

    public void setLevel(int level) {
        this.level = level;
    }

    public float getExperienceProgress() {
        return experienceProgress;
    }

    public void setExperienceProgress(float experienceProgress) {
        this.experienceProgress = experienceProgress;
    }

    public String getDeathCause() {
        return deathCause;
    }

    public void setDeathCause(String deathCause) {
        this.deathCause = deathCause;
    }

    public LocalDateTime getDeathTime() {
        return deathTime;
    }

    public void setDeathTime(LocalDateTime deathTime) {
        this.deathTime = deathTime;
    }

    public String getDeathLocation() {
        return deathLocation;
    }

    public void setDeathLocation(String deathLocation) {
        this.deathLocation = deathLocation;
    }

    public String getKiller() {
        return killer;
    }

    public void setKiller(String killer) {
        this.killer = killer;
    }

    public boolean isClaimed() {
        return claimed;
    }

    public void setClaimed(boolean claimed) {
        this.claimed = claimed;
    }

    public UUID getGrantedBy() {
        return grantedBy;
    }

    public void setGrantedBy(UUID grantedBy) {
        this.grantedBy = grantedBy;
    }

    public String getGrantedByName() {
        return grantedByName;
    }

    public void setGrantedByName(String grantedByName) {
        this.grantedByName = grantedByName;
    }

    public LocalDateTime getGrantedTime() {
        return grantedTime;
    }

    public void setGrantedTime(LocalDateTime grantedTime) {
        this.grantedTime = grantedTime;
    }
}
