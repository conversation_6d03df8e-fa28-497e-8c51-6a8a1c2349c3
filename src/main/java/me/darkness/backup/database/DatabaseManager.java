package me.darkness.backup.database;

import me.darkness.backup.Main;
import me.darkness.backup.database.providers.FlatProvider;
import me.darkness.backup.database.providers.MongoProvider;
import me.darkness.backup.database.providers.MySQLProvider;
import me.darkness.backup.models.BackupData;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static org.bukkit.Bukkit.getServer;

public class DatabaseManager {

    private final Main plugin;
    private DatabaseProvider provider;

    public DatabaseManager(Main plugin) {
        this.plugin = plugin;
    }

    public void initialize() {
        String databaseType = plugin.getConfiguration().getDatabaseType().toLowerCase();

        switch (databaseType) {
            case "mysql":
                provider = new MySQLProvider(plugin);
                break;
            case "mongo":
            case "mongodb":
                provider = new MongoProvider(plugin);
                break;
            default:
                provider = new FlatProvider(plugin);
                break;
        }

        try {
            provider.initialize().join();
            getServer().getConsoleSender().sendMessage("§8[§a§l777-Backupy§8] §f§nZaladowano baze danych!§r §8(§a§l" + databaseType.toUpperCase() + "§r§8)");
        } catch (Exception e) {
            plugin.getLogger().severe("Blad z polaczeniem z bazą danych " + databaseType.toUpperCase() + ": " + e.getMessage());
            e.printStackTrace();
            if (!databaseType.equals("flat")) {
                getServer().getConsoleSender().sendMessage("§8[§4§l777-Backupy§8] §4Wystapil blad... §cPrzywracanie bazy danych FLAT");
                provider = new FlatProvider(plugin);
                provider.initialize().join();
            }
        }
    }

    public CompletableFuture<Void> saveBackup(BackupData backupData) {
        return provider.saveBackup(backupData);
    }

    public CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid) {
        return provider.getPlayerBackups(playerUuid);
    }

    public CompletableFuture<BackupData> getBackupById(String backupId) {
        return provider.getBackupById(backupId);
    }

    public CompletableFuture<Void> updateBackup(BackupData backupData) {
        return provider.updateBackup(backupData);
    }

    public CompletableFuture<Void> deleteBackup(String backupId) {
        return provider.deleteBackup(backupId);
    }

    public CompletableFuture<List<BackupData>> getAllBackups() {
        return provider.getAllBackups();
    }

    public void disconnect() {
        if (provider != null) {
            provider.disconnect();
        }
    }
}
