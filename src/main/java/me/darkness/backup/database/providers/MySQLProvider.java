package me.darkness.backup.database.providers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonNull;
import com.google.gson.JsonParser;
import com.google.gson.JsonPrimitive;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import me.darkness.backup.Main;
import me.darkness.backup.database.DatabaseProvider;
import me.darkness.backup.models.BackupData;
import me.darkness.backup.utils.Utils;
import org.bukkit.inventory.ItemStack;

import java.sql.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class MySQLProvider implements DatabaseProvider {

    private final Main plugin;
    private final Gson gson;
    private HikariDataSource dataSource;

    public MySQLProvider(Main plugin) {
        this.plugin = plugin;
        this.gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new Utils.LocalDateTimeAdapter())
                .registerTypeAdapter(ItemStack.class, new Utils.ItemStackSerializer())
                .registerTypeAdapter(ItemStack[].class, new Utils.ItemStackArraySerializer())
                .disableHtmlEscaping()
                .serializeNulls()
                .setLenient()
                .create();
    }

    @Override
    public CompletableFuture<Void> initialize() {
        return CompletableFuture.runAsync(() -> {
            try {
                setupDataSource();
                createTables();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to initialize MySQL database", e);
            }
        });
    }

    private void setupDataSource() {
        HikariConfig config = new HikariConfig();
        String uri = plugin.getConfiguration().getDatabaseUri();
        if (uri.isEmpty()) {
            uri = "******************************************************=";
        }
        config.setJdbcUrl(uri);
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);
        
        dataSource = new HikariDataSource(config);
    }

    private void createTables() throws SQLException {
        String createTableSQL = """
            CREATE TABLE IF NOT EXISTS backups (
                id VARCHAR(36) PRIMARY KEY,
                player_uuid VARCHAR(36) NOT NULL,
                player_name VARCHAR(16) NOT NULL,
                inventory TEXT,
                armor TEXT,
                off_hand TEXT,
                experience INT NOT NULL,
                level INT NOT NULL,
                experience_progress FLOAT NOT NULL,
                death_cause TEXT NOT NULL,
                death_time TIMESTAMP NOT NULL,
                death_location TEXT NOT NULL,
                claimed BOOLEAN NOT NULL DEFAULT FALSE,
                granted_by VARCHAR(36),
                granted_by_name VARCHAR(16),
                granted_time TIMESTAMP,
                INDEX idx_player_uuid (player_uuid),
                INDEX idx_claimed (claimed),
                INDEX idx_granted_by (granted_by)
            )
            """;

        try (Connection connection = dataSource.getConnection();
             Statement statement = connection.createStatement()) {
            statement.execute(createTableSQL);

            migrateKillerColumn(connection);
        }
    }

    private void migrateKillerColumn(Connection connection) throws SQLException {
        String checkColumnSQL = """
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'backups'
            AND COLUMN_NAME = 'killer'
            """;

        try (PreparedStatement statement = connection.prepareStatement(checkColumnSQL);
             ResultSet resultSet = statement.executeQuery()) {

            if (resultSet.next() && resultSet.getInt(1) == 0) {
                String addKillerColumnSQL = "ALTER TABLE backups ADD COLUMN killer VARCHAR(16)";
                try (Statement alterStatement = connection.createStatement()) {
                    alterStatement.execute(addKillerColumnSQL);
                }
            } else {
            }
        }
    }

    @Override
    public CompletableFuture<Void> saveBackup(BackupData backupData) {
        return CompletableFuture.runAsync(() -> {
            String sql = """
                INSERT INTO backups (id, player_uuid, player_name, inventory, armor, off_hand,
                experience, level, experience_progress, death_cause, death_time, death_location,
                claimed, granted_by, granted_by_name, granted_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;
            
            try (Connection connection = dataSource.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, backupData.getId());
                statement.setString(2, backupData.getPlayerUuid().toString());
                statement.setString(3, backupData.getPlayerName());
                statement.setString(4, serializeItemArray(backupData.getInventory()));
                statement.setString(5, serializeItemArray(backupData.getArmor()));
                statement.setString(6, serializeItem(backupData.getOffHand()));
                statement.setInt(7, backupData.getExperience());
                statement.setInt(8, backupData.getLevel());
                statement.setFloat(9, backupData.getExperienceProgress());
                statement.setString(10, backupData.getDeathCause());
                statement.setTimestamp(11, Timestamp.valueOf(backupData.getDeathTime()));
                statement.setString(12, backupData.getDeathLocation());
                statement.setBoolean(13, backupData.isClaimed());
                statement.setString(14, backupData.getGrantedBy() != null ? backupData.getGrantedBy().toString() : null);
                statement.setString(15, backupData.getGrantedByName());
                statement.setTimestamp(16, backupData.getGrantedTime() != null ? Timestamp.valueOf(backupData.getGrantedTime()) : null);
                
                statement.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to save backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM backups WHERE player_uuid = ? ORDER BY death_time DESC";
            List<BackupData> backups = new ArrayList<>();
            
            try (Connection connection = dataSource.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, playerUuid.toString());
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    while (resultSet.next()) {
                        backups.add(mapResultSetToBackupData(resultSet));
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException("Failed to get player backups", e);
            }
            
            return backups;
        });
    }

    @Override
    public CompletableFuture<BackupData> getBackupById(String backupId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM backups WHERE id = ?";
            
            try (Connection connection = dataSource.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, backupId);
                
                try (ResultSet resultSet = statement.executeQuery()) {
                    if (resultSet.next()) {
                        return mapResultSetToBackupData(resultSet);
                    }
                }
            } catch (SQLException e) {
                throw new RuntimeException("Failed to get backup by ID", e);
            }
            
            return null;
        });
    }

    @Override
    public CompletableFuture<Void> updateBackup(BackupData backupData) {
        return CompletableFuture.runAsync(() -> {
            String sql = """
                UPDATE backups SET player_name = ?, inventory = ?, armor = ?, off_hand = ?,
                experience = ?, level = ?, experience_progress = ?, death_cause = ?,
                death_time = ?, death_location = ?, claimed = ?, granted_by = ?,
                granted_by_name = ?, granted_time = ? WHERE id = ?
                """;
            
            try (Connection connection = dataSource.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, backupData.getPlayerName());
                statement.setString(2, serializeItemArray(backupData.getInventory()));
                statement.setString(3, serializeItemArray(backupData.getArmor()));
                statement.setString(4, serializeItem(backupData.getOffHand()));
                statement.setInt(5, backupData.getExperience());
                statement.setInt(6, backupData.getLevel());
                statement.setFloat(7, backupData.getExperienceProgress());
                statement.setString(8, backupData.getDeathCause());
                statement.setTimestamp(9, Timestamp.valueOf(backupData.getDeathTime()));
                statement.setString(10, backupData.getDeathLocation());
                statement.setBoolean(11, backupData.isClaimed());
                statement.setString(12, backupData.getGrantedBy() != null ? backupData.getGrantedBy().toString() : null);
                statement.setString(13, backupData.getGrantedByName());
                statement.setTimestamp(14, backupData.getGrantedTime() != null ? Timestamp.valueOf(backupData.getGrantedTime()) : null);
                statement.setString(15, backupData.getId());
                
                statement.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to update backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<Void> deleteBackup(String backupId) {
        return CompletableFuture.runAsync(() -> {
            String sql = "DELETE FROM backups WHERE id = ?";
            
            try (Connection connection = dataSource.getConnection();
                 PreparedStatement statement = connection.prepareStatement(sql)) {
                
                statement.setString(1, backupId);
                statement.executeUpdate();
            } catch (SQLException e) {
                throw new RuntimeException("Failed to delete backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getAllBackups() {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM backups ORDER BY death_time DESC";
            List<BackupData> backups = new ArrayList<>();
            
            try (Connection connection = dataSource.getConnection();
                 Statement statement = connection.createStatement();
                 ResultSet resultSet = statement.executeQuery(sql)) {
                
                while (resultSet.next()) {
                    backups.add(mapResultSetToBackupData(resultSet));
                }
            } catch (SQLException e) {
                throw new RuntimeException("Failed to get all backups", e);
            }
            
            return backups;
        });
    }

    private BackupData mapResultSetToBackupData(ResultSet resultSet) throws SQLException {
        BackupData backup = new BackupData();
        backup.setId(resultSet.getString("id"));
        backup.setPlayerUuid(UUID.fromString(resultSet.getString("player_uuid")));
        backup.setPlayerName(resultSet.getString("player_name"));
        backup.setInventory(deserializeItemArray(resultSet.getString("inventory")));
        backup.setArmor(deserializeItemArray(resultSet.getString("armor")));
        backup.setOffHand(deserializeItem(resultSet.getString("off_hand")));
        backup.setExperience(resultSet.getInt("experience"));
        backup.setLevel(resultSet.getInt("level"));
        backup.setExperienceProgress(resultSet.getFloat("experience_progress"));
        backup.setDeathCause(resultSet.getString("death_cause"));
        backup.setDeathTime(resultSet.getTimestamp("death_time").toLocalDateTime());
        backup.setDeathLocation(resultSet.getString("death_location"));
        backup.setClaimed(resultSet.getBoolean("claimed"));
        
        String grantedByString = resultSet.getString("granted_by");
        if (grantedByString != null) {
            backup.setGrantedBy(UUID.fromString(grantedByString));
        }
        
        backup.setGrantedByName(resultSet.getString("granted_by_name"));
        
        Timestamp grantedTime = resultSet.getTimestamp("granted_time");
        if (grantedTime != null) {
            backup.setGrantedTime(grantedTime.toLocalDateTime());
        }
        
        return backup;
    }

    private String serializeItem(ItemStack item) {
        if (item == null) return null;
        try {
            Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();
            JsonElement element = serializer.serialize(item, ItemStack.class, null);
            return element.getAsString();
        } catch (Exception e) {
            return null;
        }
    }

    private String serializeItemArray(ItemStack[] items) {
        if (items == null) return null;
        try {
            JsonArray array = new JsonArray();
            for (ItemStack item : items) {
                if (item != null) {
                    Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();
                    JsonElement element = serializer.serialize(item, ItemStack.class, null);
                    array.add(element);
                } else {
                    array.add(JsonNull.INSTANCE);
                }
            }
            return array.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private ItemStack deserializeItem(String itemData) {
        if (itemData == null) return null;
        try {
            Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();
            JsonElement element = new JsonPrimitive(itemData);
            return serializer.deserialize(element, ItemStack.class, null);
        } catch (Exception e) {
            return null;
        }
    }

    private ItemStack[] deserializeItemArray(String itemData) {
        if (itemData == null) return null;
        try {
            JsonArray array = JsonParser.parseString(itemData).getAsJsonArray();
            ItemStack[] items = new ItemStack[array.size()];
            Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();

            for (int i = 0; i < array.size(); i++) {
                JsonElement element = array.get(i);
                if (element.isJsonNull()) {
                    items[i] = null;
                } else {
                    items[i] = serializer.deserialize(element, ItemStack.class, null);
                }
            }
            return items;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void disconnect() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
        }
    }
}
