package me.darkness.backup.database.providers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.Sorts;
import me.darkness.backup.Main;
import me.darkness.backup.database.DatabaseProvider;
import me.darkness.backup.models.BackupData;
import me.darkness.backup.utils.Utils;
import org.bson.Document;
import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class MongoProvider implements DatabaseProvider {

    private final Main plugin;
    private final Gson gson;
    private MongoClient mongoClient;
    private MongoDatabase database;
    private MongoCollection<Document> backupsCollection;

    public MongoProvider(Main plugin) {
        this.plugin = plugin;
        this.gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new Utils.LocalDateTimeAdapter())
                .registerTypeAdapter(ItemStack.class, new Utils.ItemStackSerializer())
                .registerTypeAdapter(ItemStack[].class, new Utils.ItemStackArraySerializer())
                .disableHtmlEscaping()
                .serializeNulls()
                .setLenient()
                .create();
    }

    @Override
    public CompletableFuture<Void> initialize() {
        return CompletableFuture.runAsync(() -> {
            try {
                String connectionString = plugin.getConfiguration().getDatabaseUri();
                if (connectionString.isEmpty()) {
                    connectionString = "mongodb://localhost:27017";
                }
                String databaseName = "backups";
                
                mongoClient = MongoClients.create(connectionString);
                database = mongoClient.getDatabase(databaseName);
                backupsCollection = database.getCollection("backups");
                
                // Create indexes for better performance
                backupsCollection.createIndex(new Document("playerUuid", 1));
                backupsCollection.createIndex(new Document("claimed", 1));
                backupsCollection.createIndex(new Document("grantedBy", 1));
                backupsCollection.createIndex(new Document("deathTime", -1));
                
            } catch (Exception e) {
                throw new RuntimeException("Failed to initialize MongoDB database", e);
            }
        });
    }

    @Override
    public CompletableFuture<Void> saveBackup(BackupData backupData) {
        return CompletableFuture.runAsync(() -> {
            try {
                Document document = backupDataToDocument(backupData);
                backupsCollection.insertOne(document);
            } catch (Exception e) {
                throw new RuntimeException("Failed to save backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<BackupData> backups = new ArrayList<>();
                
                backupsCollection.find(Filters.eq("playerUuid", playerUuid.toString()))
                        .sort(Sorts.descending("deathTime"))
                        .forEach(document -> {
                            BackupData backup = documentToBackupData(document);
                            if (backup != null) {
                                backups.add(backup);
                            }
                        });
                
                return backups;
            } catch (Exception e) {
                throw new RuntimeException("Failed to get player backups", e);
            }
        });
    }

    @Override
    public CompletableFuture<BackupData> getBackupById(String backupId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                Document document = backupsCollection.find(Filters.eq("_id", backupId)).first();
                return document != null ? documentToBackupData(document) : null;
            } catch (Exception e) {
                throw new RuntimeException("Failed to get backup by ID", e);
            }
        });
    }

    @Override
    public CompletableFuture<Void> updateBackup(BackupData backupData) {
        return CompletableFuture.runAsync(() -> {
            try {
                Document document = backupDataToDocument(backupData);
                backupsCollection.replaceOne(Filters.eq("_id", backupData.getId()), document);
            } catch (Exception e) {
                throw new RuntimeException("Failed to update backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<Void> deleteBackup(String backupId) {
        return CompletableFuture.runAsync(() -> {
            try {
                backupsCollection.deleteOne(Filters.eq("_id", backupId));
            } catch (Exception e) {
                throw new RuntimeException("Failed to delete backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getAllBackups() {
        return CompletableFuture.supplyAsync(() -> {
            try {
                List<BackupData> backups = new ArrayList<>();
                
                backupsCollection.find()
                        .sort(Sorts.descending("deathTime"))
                        .forEach(document -> {
                            BackupData backup = documentToBackupData(document);
                            if (backup != null) {
                                backups.add(backup);
                            }
                        });
                
                return backups;
            } catch (Exception e) {
                throw new RuntimeException("Failed to get all backups", e);
            }
        });
    }

    private Document backupDataToDocument(BackupData backupData) {
        Document document = new Document();
        document.put("_id", backupData.getId());
        document.put("playerUuid", backupData.getPlayerUuid().toString());
        document.put("playerName", backupData.getPlayerName());
        document.put("inventory", gson.toJson(backupData.getInventory()));
        document.put("armor", gson.toJson(backupData.getArmor()));
        document.put("offHand", gson.toJson(backupData.getOffHand()));
        document.put("experience", backupData.getExperience());
        document.put("level", backupData.getLevel());
        document.put("experienceProgress", backupData.getExperienceProgress());
        document.put("deathCause", backupData.getDeathCause());
        document.put("deathTime", backupData.getDeathTime().toString());
        document.put("deathLocation", backupData.getDeathLocation());
        document.put("killer", backupData.getKiller());
        document.put("claimed", backupData.isClaimed());
        
        if (backupData.getGrantedBy() != null) {
            document.put("grantedBy", backupData.getGrantedBy().toString());
        }
        
        document.put("grantedByName", backupData.getGrantedByName());
        
        if (backupData.getGrantedTime() != null) {
            document.put("grantedTime", backupData.getGrantedTime().toString());
        }
        
        return document;
    }

    private BackupData documentToBackupData(Document document) {
        try {
            BackupData backup = new BackupData();
            backup.setId(document.getString("_id"));
            backup.setPlayerUuid(UUID.fromString(document.getString("playerUuid")));
            backup.setPlayerName(document.getString("playerName"));
            backup.setInventory(gson.fromJson(document.getString("inventory"), ItemStack[].class));
            backup.setArmor(gson.fromJson(document.getString("armor"), ItemStack[].class));
            backup.setOffHand(gson.fromJson(document.getString("offHand"), ItemStack.class));
            backup.setExperience(document.getInteger("experience"));
            backup.setLevel(document.getInteger("level"));
            backup.setExperienceProgress(document.getDouble("experienceProgress").floatValue());
            backup.setDeathCause(document.getString("deathCause"));
            backup.setDeathTime(LocalDateTime.parse(document.getString("deathTime")));
            backup.setDeathLocation(document.getString("deathLocation"));
            backup.setKiller(document.getString("killer"));
            backup.setClaimed(document.getBoolean("claimed"));
            
            String grantedByString = document.getString("grantedBy");
            if (grantedByString != null) {
                backup.setGrantedBy(UUID.fromString(grantedByString));
            }
            
            backup.setGrantedByName(document.getString("grantedByName"));
            
            String grantedTimeString = document.getString("grantedTime");
            if (grantedTimeString != null) {
                backup.setGrantedTime(LocalDateTime.parse(grantedTimeString));
            }
            
            return backup;
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public void disconnect() {
        if (mongoClient != null) {
            mongoClient.close();
        }
    }
}
