package me.darkness.backup.database.providers;

import com.google.gson.*;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import me.darkness.backup.Main;
import me.darkness.backup.database.DatabaseProvider;
import me.darkness.backup.models.BackupData;
import me.darkness.backup.utils.Utils;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class FlatProvider implements DatabaseProvider {

    private final Main plugin;
    private final File dataFolder;
    private final Gson gson;

    public FlatProvider(Main plugin) {
        this.plugin = plugin;
        this.dataFolder = new File(plugin.getDataFolder(), "backups");
        this.gson = new GsonBuilder()
                .registerTypeAdapter(LocalDateTime.class, new Utils.LocalDateTimeAdapter())
                .registerTypeAdapter(ItemStack.class, new Utils.ItemStackSerializer())
                .registerTypeAdapter(ItemStack[].class, new Utils.ItemStackArraySerializer())
                .disableHtmlEscaping()
                .serializeNulls()
                .setPrettyPrinting()
                .setLenient()
                .create();
    }

    @Override
    public CompletableFuture<Void> initialize() {
        return CompletableFuture.runAsync(() -> {
            if (!dataFolder.exists()) {
                dataFolder.mkdirs();
            }
        });
    }

    @Override
    public CompletableFuture<Void> saveBackup(BackupData backupData) {
        return CompletableFuture.runAsync(() -> {
            try {

                File playerFolder = new File(dataFolder, backupData.getPlayerUuid().toString());
                if (!playerFolder.exists()) {
                    playerFolder.mkdirs();
                }

                File backupFile = new File(playerFolder, backupData.getId() + ".json");

                String jsonContent = createSafeJson(backupData);

                try (FileWriter writer = new FileWriter(backupFile)) {
                    writer.write(jsonContent);
                }

            } catch (Exception e) {
                plugin.getLogger().severe("Nie mozna zapisac backupa: " + e.getMessage());
                e.printStackTrace();
                throw new RuntimeException("Failed to save backup", e);
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {

            List<BackupData> backups = new ArrayList<>();
            File playerFolder = new File(dataFolder, playerUuid.toString());

            if (!playerFolder.exists()) {
                return backups;
            }

            File[] files = playerFolder.listFiles((dir, name) -> name.endsWith(".json"));

            if (files != null) {
                for (File file : files) {
                    try {
                        BackupData backup = parseBackupJson(file);
                        if (backup != null) {
                            backups.add(backup);
                        }
                    } catch (Exception e) {
                        File corruptedFile = new File(file.getParentFile(), file.getName() + ".corrupted");
                        file.renameTo(corruptedFile);
                    }
                }
            }
            return backups;
        });
    }

    @Override
    public CompletableFuture<BackupData> getBackupById(String backupId) {
        return CompletableFuture.supplyAsync(() -> {
            File[] playerFolders = dataFolder.listFiles(File::isDirectory);
            if (playerFolders != null) {
                for (File playerFolder : playerFolders) {
                    File backupFile = new File(playerFolder, backupId + ".json");
                    if (backupFile.exists()) {
                        try {
                            return parseBackupJson(backupFile);
                        } catch (Exception e) {
                            plugin.getLogger().warning("Failed to load backup: " + backupId + " - " + e.getMessage());
                        }
                    }
                }
            }
            return null;
        });
    }

    @Override
    public CompletableFuture<Void> updateBackup(BackupData backupData) {
        return saveBackup(backupData);
    }

    @Override
    public CompletableFuture<Void> deleteBackup(String backupId) {
        return CompletableFuture.runAsync(() -> {
            File[] playerFolders = dataFolder.listFiles(File::isDirectory);
            if (playerFolders != null) {
                for (File playerFolder : playerFolders) {
                    File backupFile = new File(playerFolder, backupId + ".json");
                    if (backupFile.exists()) {
                        backupFile.delete();
                        return;
                    }
                }
            }
        });
    }

    @Override
    public CompletableFuture<List<BackupData>> getAllBackups() {
        return CompletableFuture.supplyAsync(() -> {
            List<BackupData> allBackups = new ArrayList<>();
            File[] playerFolders = dataFolder.listFiles(File::isDirectory);
            
            if (playerFolders != null) {
                for (File playerFolder : playerFolders) {
                    try {
                        UUID playerUuid = UUID.fromString(playerFolder.getName());
                        List<BackupData> playerBackups = getPlayerBackups(playerUuid).join();
                        allBackups.addAll(playerBackups);
                    } catch (IllegalArgumentException ignored) {
                    }
                }
            }
            
            return allBackups;
        });
    }

    private BackupData parseBackupJson(File file) throws Exception {
        try (FileReader reader = new FileReader(file)) {
            // Try to parse as SafeBackupData first
            SafeBackupData safeData = gson.fromJson(reader, SafeBackupData.class);
            return convertToBackupData(safeData);
        } catch (Exception e) {
            // Fallback: try to parse as old BackupData format
            try (FileReader reader2 = new FileReader(file)) {
                return gson.fromJson(reader2, BackupData.class);
            }
        }
    }

    private BackupData convertToBackupData(SafeBackupData safeData) {
        BackupData backup = new BackupData();
        backup.setId(safeData.id);
        backup.setPlayerUuid(java.util.UUID.fromString(safeData.playerUuid));
        backup.setPlayerName(safeData.playerName);
        backup.setExperience(safeData.experience);
        backup.setLevel(safeData.level);
        backup.setExperienceProgress(safeData.experienceProgress);
        backup.setDeathCause(safeData.deathCause);
        backup.setDeathTime(java.time.LocalDateTime.parse(safeData.deathTime));
        backup.setDeathLocation(safeData.deathLocation);
        backup.setKiller(safeData.killer);
        backup.setClaimed(safeData.claimed);

        if (safeData.grantedBy != null) {
            backup.setGrantedBy(java.util.UUID.fromString(safeData.grantedBy));
        }
        backup.setGrantedByName(safeData.grantedByName);
        if (safeData.grantedTime != null) {
            backup.setGrantedTime(java.time.LocalDateTime.parse(safeData.grantedTime));
        }

        // Deserialize ItemStack arrays
        backup.setInventory(deserializeItemArray(safeData.inventory));
        backup.setArmor(deserializeItemArray(safeData.armor));
        backup.setOffHand(deserializeItem(safeData.offHand));

        return backup;
    }

    private ItemStack deserializeItem(String itemData) {
        if (itemData == null) return null;
        try {
            Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();
            JsonElement element = new JsonPrimitive(itemData);
            return serializer.deserialize(element, ItemStack.class, null);
        } catch (Exception e) {
            return null;
        }
    }

    private ItemStack[] deserializeItemArray(String[] itemsData) {
        if (itemsData == null) return null;
        ItemStack[] result = new ItemStack[itemsData.length];
        for (int i = 0; i < itemsData.length; i++) {
            result[i] = deserializeItem(itemsData[i]);
        }
        return result;
    }



    private String createSafeJson(BackupData backupData) {
        // Create a copy without ItemStack arrays to avoid GSON issues
        SafeBackupData safeData = new SafeBackupData();
        safeData.id = backupData.getId();
        safeData.playerUuid = backupData.getPlayerUuid().toString();
        safeData.playerName = backupData.getPlayerName();
        safeData.experience = backupData.getExperience();
        safeData.level = backupData.getLevel();
        safeData.experienceProgress = backupData.getExperienceProgress();
        safeData.deathCause = backupData.getDeathCause();
        safeData.deathTime = backupData.getDeathTime().toString();
        safeData.deathLocation = backupData.getDeathLocation();
        safeData.killer = backupData.getKiller();
        safeData.claimed = backupData.isClaimed();
        safeData.grantedBy = backupData.getGrantedBy() != null ? backupData.getGrantedBy().toString() : null;
        safeData.grantedByName = backupData.getGrantedByName();
        safeData.grantedTime = backupData.getGrantedTime() != null ? backupData.getGrantedTime().toString() : null;

        // Serialize ItemStack arrays separately
        safeData.inventory = serializeItemArray(backupData.getInventory());
        safeData.armor = serializeItemArray(backupData.getArmor());
        safeData.offHand = serializeItem(backupData.getOffHand());

        return gson.toJson(safeData);
    }

    private String serializeItem(ItemStack item) {
        if (item == null) return null;
        try {
            Utils.ItemStackSerializer serializer = new Utils.ItemStackSerializer();
            JsonElement element = serializer.serialize(item, ItemStack.class, null);
            return element.getAsString();
        } catch (Exception e) {
            return null;
        }
    }

    private String[] serializeItemArray(ItemStack[] items) {
        if (items == null) return null;
        String[] result = new String[items.length];
        for (int i = 0; i < items.length; i++) {
            result[i] = serializeItem(items[i]);
        }
        return result;
    }

    private static class SafeBackupData {
        String id;
        String playerUuid;
        String playerName;
        String[] inventory;
        String[] armor;
        String offHand;
        int experience;
        int level;
        float experienceProgress;
        String deathCause;
        String deathTime;
        String deathLocation;
        String killer;
        boolean claimed;
        String grantedBy;
        String grantedByName;
        String grantedTime;
    }

    @Override
    public void disconnect() {
        // No connection to close for flat file provider
    }
}
