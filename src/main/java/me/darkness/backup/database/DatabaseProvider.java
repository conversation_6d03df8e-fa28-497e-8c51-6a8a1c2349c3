package me.darkness.backup.database;

import me.darkness.backup.models.BackupData;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public interface DatabaseProvider {

    CompletableFuture<Void> initialize();

    CompletableFuture<Void> saveBackup(BackupData backupData);

    CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid);

    CompletableFuture<BackupData> getBackupById(String backupId);

    CompletableFuture<Void> updateBackup(BackupData backupData);

    CompletableFuture<Void> deleteBackup(String backupId);

    CompletableFuture<List<BackupData>> getAllBackups();

    void disconnect();
}
