package me.darkness.backup.webhook;

import com.google.gson.JsonObject;
import me.darkness.backup.Main;
import me.darkness.backup.models.BackupData;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.time.format.DateTimeFormatter;

public class WebhookManager {

    private final Main plugin;

    public WebhookManager(Main plugin) {
        this.plugin = plugin;
    }

    public void sendBackupGrantedNotification(BackupData backup) {
        if (!plugin.getConfiguration().isDiscordEnabled()) {
            return;
        }

        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                sendWebhook(createBackupGrantedEmbed(backup));
            } catch (Exception e) {
                plugin.getLogger().warning("Failed to send Discord webhook: " + e.getMessage());
            }
        });
    }

    private JsonObject createBackupGrantedEmbed(BackupData backup) {
        JsonObject webhook = new JsonObject();
        JsonObject embed = new JsonObject();

        String title = replacePlaceholders(plugin.getConfiguration().getDiscordEmbedTitle(), backup);
        String description = replacePlaceholders(plugin.getConfiguration().getDiscordEmbedDescription(), backup);

        embed.addProperty("title", title);
        embed.addProperty("description", description);
        embed.addProperty("color", parseColor(plugin.getConfiguration().getDiscordEmbedColor()));

        if (plugin.getConfiguration().getDiscordEmbedTimestamp()) {
            embed.addProperty("timestamp", Instant.now().toString());
        }

        if (plugin.getConfiguration().isDiscordEmbedThumbnailEnabled()) {
            String thumbnail = replacePlaceholders(plugin.getConfiguration().getDiscordEmbedThumbnail(), backup);
            if (!thumbnail.isEmpty()) {
                JsonObject thumbnailObj = new JsonObject();
                thumbnailObj.addProperty("url", thumbnail);
                embed.add("thumbnail", thumbnailObj);
            }
        }

        String authorName = plugin.getConfiguration().getDiscordEmbedAuthorName();
        String authorIconUrl = plugin.getConfiguration().getDiscordEmbedAuthorIconUrl();
        if (!authorName.isEmpty()) {
            JsonObject author = new JsonObject();
            author.addProperty("name", replacePlaceholders(authorName, backup));
            if (!authorIconUrl.isEmpty()) {
                author.addProperty("icon_url", replacePlaceholders(authorIconUrl, backup));
            }
            embed.add("author", author);
        }

        JsonObject footer = new JsonObject();
        footer.addProperty("text", replacePlaceholders(plugin.getConfiguration().getDiscordEmbedFooter(), backup));
        embed.add("footer", footer);

        com.google.gson.JsonArray fieldsArray = new com.google.gson.JsonArray();
        org.bukkit.configuration.ConfigurationSection fieldsSection = plugin.getConfiguration().getDiscordEmbedFields();

        if (fieldsSection != null) {
            for (String fieldKey : fieldsSection.getKeys(false)) {
                String fieldName = fieldsSection.getString(fieldKey + ".name", "");
                String fieldValue = fieldsSection.getString(fieldKey + ".value", "");
                boolean fieldInline = fieldsSection.getBoolean(fieldKey + ".inline", true);

                if (!fieldName.isEmpty() && !fieldValue.isEmpty()) {
                    addField(fieldsArray,
                        replacePlaceholders(fieldName, backup),
                        replacePlaceholders(fieldValue, backup),
                        fieldInline
                    );
                }
            }
        }

        embed.add("fields", fieldsArray);

        com.google.gson.JsonArray embeds = new com.google.gson.JsonArray();
        embeds.add(embed);
        webhook.add("embeds", embeds);

        return webhook;
    }

    private String replacePlaceholders(String text, BackupData backup) {
        if (text == null) return "";

        return text
                .replace("{player}", backup.getPlayerName() != null ? backup.getPlayerName() : "Unknown")
                .replace("{player_uuid}", backup.getPlayerUuid() != null ? backup.getPlayerUuid().toString() : "")
                .replace("{granted_by}", backup.getGrantedByName() != null ? backup.getGrantedByName() : "System")
                .replace("{death_cause}", backup.getDeathCause() != null ? backup.getDeathCause() : "Unknown")
                .replace("{death_date}", backup.getDeathTime() != null ? backup.getDeathTime().format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss")) : "Unknown")
                .replace("{death_location}", backup.getDeathLocation() != null ? backup.getDeathLocation() : "Unknown")
                .replace("{level}", String.valueOf(backup.getLevel()))
                .replace("{experience}", String.valueOf(backup.getExperience()));
    }

    private void addField(com.google.gson.JsonArray fieldsArray, String name, String value, boolean inline) {
        JsonObject field = new JsonObject();
        field.addProperty("name", name);
        field.addProperty("value", value);
        field.addProperty("inline", inline);
        fieldsArray.add(field);
    }

    private int parseColor(String colorString) {
        try {
            if (colorString.startsWith("#")) {
                return Integer.parseInt(colorString.substring(1), 16);
            } else {
                return Integer.parseInt(colorString, 16);
            }
        } catch (NumberFormatException e) {
            return 0x00FF00;
        }
    }

    private void sendWebhook(JsonObject payload) throws IOException {
        String webhookUrl = plugin.getConfiguration().getDiscordWebhookUrl();
        
        URL url = new URL(webhookUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);
        
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = payload.toString().getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }
        
        int responseCode = connection.getResponseCode();
        if (responseCode != 200 && responseCode != 204) {
            throw new IOException("Discord webhook returned response code: " + responseCode);
        }
    }
}
