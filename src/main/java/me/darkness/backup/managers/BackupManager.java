package me.darkness.backup.managers;

import me.darkness.backup.Main;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class BackupManager {

    private final Main plugin;

    public BackupManager(Main plugin) {
        this.plugin = plugin;
    }

    public CompletableFuture<List<BackupData>> getPlayerBackups(UUID playerUuid) {
        return plugin.getDatabaseManager().getPlayerBackups(playerUuid);
    }

    public CompletableFuture<BackupData> getBackupById(String backupId) {
        return plugin.getDatabaseManager().getBackupById(backupId);
    }

    public CompletableFuture<Void> grantBackup(String backupId, Player grantedBy) {
        return getBackupById(backupId).thenCompose(backup -> {
            if (backup == null) {
                return CompletableFuture.failedFuture(new IllegalArgumentException("Backup not found"));
            }
            
            backup.setGrantedBy(grantedBy.getUniqueId());
            backup.setGrantedByName(grantedBy.getName());
            backup.setGrantedTime(LocalDateTime.now());
            
            return plugin.getDatabaseManager().updateBackup(backup).thenRun(() -> {
                if (plugin.getConfiguration().isDiscordEnabled()) {
                    plugin.getWebhookManager().sendBackupGrantedNotification(backup);
                }
            });
        });
    }

    public CompletableFuture<Boolean> claimBackup(String backupId, Player player) {
        return getBackupById(backupId).thenCompose(backup -> {
            if (backup == null || !backup.getPlayerUuid().equals(player.getUniqueId()) ||
                backup.isClaimed() || backup.getGrantedBy() == null) {
                return CompletableFuture.completedFuture(false);
            }

            restorePlayerData(player, backup);
            backup.setClaimed(true);

            return plugin.getDatabaseManager().updateBackup(backup).thenApply(v -> true);
        });
    }

    private void restorePlayerData(Player player, BackupData backup) {
        player.getInventory().clear();

        if (backup.getInventory() != null) {
            player.getInventory().setContents(backup.getInventory());
        }

        if (backup.getArmor() != null) {
            player.getInventory().setArmorContents(backup.getArmor());
        }

        if (backup.getOffHand() != null) {
            try {
                player.getInventory().setItemInOffHand(backup.getOffHand());
            } catch (NoSuchMethodError ignored) {}
        }

        player.setLevel(backup.getLevel());
        player.setExp(backup.getExperienceProgress());
        player.setTotalExperience(backup.getExperience());
        player.updateInventory();
    }

    public CompletableFuture<List<BackupData>> getGrantedBackups(UUID playerUuid) {
        return getPlayerBackups(playerUuid).thenApply(backups ->
            backups.stream()
                    .filter(backup -> backup.getGrantedBy() != null && !backup.isClaimed())
                    .collect(Collectors.toList())
        );
    }
}
