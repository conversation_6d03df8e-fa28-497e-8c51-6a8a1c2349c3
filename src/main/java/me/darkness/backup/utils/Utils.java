package me.darkness.backup.utils;

import com.google.gson.*;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.io.BukkitObjectInputStream;
import org.bukkit.util.io.BukkitObjectOutputStream;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Base64;

public class Utils {

    public static class LocalDateTimeAdapter implements JsonSerializer<LocalDateTime>, JsonDeserializer<LocalDateTime> {

        private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

        @Override
        public JsonElement serialize(LocalDateTime localDateTime, Type type, JsonSerializationContext context) {
            return new JsonPrimitive(localDateTime.format(FORMATTER));
        }

        @Override
        public LocalDateTime deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext context) throws JsonParseException {
            return LocalDateTime.parse(jsonElement.getAsString(), FORMATTER);
        }
    }

    public static class ItemStackSerializer implements JsonSerializer<ItemStack>, JsonDeserializer<ItemStack> {

        @Override
        public JsonElement serialize(ItemStack itemStack, Type type, JsonSerializationContext context) {
            if (itemStack == null) {
                return JsonNull.INSTANCE;
            }

            try {
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);
                dataOutput.writeObject(itemStack);
                dataOutput.close();

                return new JsonPrimitive(Base64.getEncoder().encodeToString(outputStream.toByteArray()));
            } catch (IOException e) {
                throw new JsonParseException("Failed to serialize ItemStack", e);
            }
        }

        @Override
        public ItemStack deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext context) throws JsonParseException {
            if (jsonElement.isJsonNull()) {
                return null;
            }

            try {
                byte[] data = Base64.getDecoder().decode(jsonElement.getAsString());
                ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
                BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);
                ItemStack itemStack = (ItemStack) dataInput.readObject();
                dataInput.close();

                return itemStack;
            } catch (IOException | ClassNotFoundException e) {
                throw new JsonParseException("Failed to deserialize ItemStack", e);
            }
        }
    }

    public static class ItemStackArraySerializer implements JsonSerializer<ItemStack[]>, JsonDeserializer<ItemStack[]> {

        @Override
        public JsonElement serialize(ItemStack[] itemStacks, Type type, JsonSerializationContext context) {
            if (itemStacks == null) {
                return JsonNull.INSTANCE;
            }

            JsonArray array = new JsonArray();
            for (ItemStack itemStack : itemStacks) {
                array.add(context.serialize(itemStack, ItemStack.class));
            }

            return array;
        }

        @Override
        public ItemStack[] deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext context) throws JsonParseException {
            if (jsonElement.isJsonNull()) {
                return null;
            }
            
            JsonArray array = jsonElement.getAsJsonArray();
            ItemStack[] itemStacks = new ItemStack[array.size()];
            
            for (int i = 0; i < array.size(); i++) {
                itemStacks[i] = context.deserialize(array.get(i), ItemStack.class);
            }
            
            return itemStacks;
        }
    }
}
