package me.darkness.backup.utils;

import net.md_5.bungee.api.ChatColor;
import org.bukkit.Bukkit;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ColorUtils {

    private static final Pattern HEX_PATTERN = Pattern.compile("&#([A-Fa-f0-9]{6})");
    private static final boolean SUPPORTS_HEX = isVersionSupported();

    public static String colorize(String message) {
        if (message == null) {
            return null;
        }

        if (SUPPORTS_HEX) {
            message = translateHexColorCodes(message);
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    private static String translateHexColorCodes(String message) {
        Matcher matcher = HEX_PATTERN.matcher(message);
        StringBuffer buffer = new StringBuffer(message.length() + 4 * 8);
        
        while (matcher.find()) {
            String group = matcher.group(1);
            matcher.appendReplacement(buffer, "§x"
                    + "§" + group.charAt(0) + "§" + group.charAt(1)
                    + "§" + group.charAt(2) + "§" + group.charAt(3)
                    + "§" + group.charAt(4) + "§" + group.charAt(5));
        }
        
        return matcher.appendTail(buffer).toString();
    }

    private static boolean isVersionSupported() {
        try {
            String version = Bukkit.getBukkitVersion();
            String[] versionParts = version.split("\\.");
            int majorVersion = Integer.parseInt(versionParts[1]);

            return majorVersion >= 16;
        } catch (Exception e) {
            return true;
        }
    }

    public static String stripColor(String message) {
        if (message == null) {
            return null;
        }
        return ChatColor.stripColor(colorize(message));
    }
}
