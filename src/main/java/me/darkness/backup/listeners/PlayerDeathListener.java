package me.darkness.backup.listeners;

import me.darkness.backup.Main;
import me.darkness.backup.models.BackupData;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.inventory.ItemStack;

import java.time.LocalDateTime;

public class PlayerDeathListener implements Listener {

    private final Main plugin;

    public PlayerDeathListener(Main plugin) {
        this.plugin = plugin;
    }

    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerDeath(PlayerDeathEvent event) {
        Player player = event.getEntity();

        BackupData backupData = new BackupData(
                player.getUniqueId(),
                player.getName(),
                player.getInventory().getContents(),
                player.getInventory().getArmorContents(),
                getOffHandItem(player),
                player.getTotalExperience(),
                player.getLevel(),
                player.getExp(),
                getDeat<PERSON><PERSON><PERSON><PERSON>(event),
                LocalDateTime.now(),
                getLocationString(player)
        );

        backupData.setKiller(getKillerName(player));

        plugin.getDatabaseManager().saveBackup(backupData).exceptionally(throwable -> {
            throwable.printStackTrace();
            return null;
        });
    }

    private ItemStack getOffHandItem(Player player) {
        try {
            return player.getInventory().getItemInOffHand();
        } catch (NoSuchMethodError e) {
            return null;
        }
    }

    private String getDeathCause(PlayerDeathEvent event) {
        Player player = event.getEntity();
        Player killer = player.getKiller();

        if (killer != null) {
            return "Zabity przez gracza: " + killer.getName();
        }

        if (event.getDeathMessage() != null && !event.getDeathMessage().isEmpty()) {
            return event.getDeathMessage();
        }

        if (event.getEntity().getLastDamageCause() != null) {
            return event.getEntity().getLastDamageCause().getCause().name();
        }

        return "Unknown";
    }

    private String getLocationString(Player player) {
        return String.format("%s: %.1f, %.1f, %.1f",
                player.getWorld().getName(),
                player.getLocation().getX(),
                player.getLocation().getY(),
                player.getLocation().getZ());
    }

    private String getKillerName(Player player) {
        Player killer = player.getKiller();
        return killer != null ? killer.getName() : null;
    }

}
