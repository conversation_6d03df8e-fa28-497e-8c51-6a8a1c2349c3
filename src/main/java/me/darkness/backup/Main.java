package me.darkness.backup;

import me.darkness.backup.commands.admin.BackupAdminCommand;
import me.darkness.backup.commands.player.ClaimBackupCommand;
import me.darkness.backup.configuration.Configuration;
import me.darkness.backup.configuration.Messages;
import me.darkness.backup.database.DatabaseManager;
import me.darkness.backup.webhook.WebhookManager;
import me.darkness.backup.gui.GuiManager;
import me.darkness.backup.listeners.PlayerDeathListener;
import me.darkness.backup.managers.BackupManager;
import org.bukkit.plugin.java.JavaPlugin;

public class Main extends JavaPlugin {

    private static Main instance;

    private Configuration configuration;
    private Messages messages;
    private DatabaseManager databaseManager;
    private BackupManager backupManager;
    private GuiManager guiManager;
    private WebhookManager webhookManager;

    @Override
    public void onEnable() {
        instance = this;

        initializeConfigurations();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Backupy§8] §f§nZaladanowano konfiguracje!§r.");
        initializeDatabase();
        initializeManagers();
        registerCommands();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Backupy§8] §f§nZarejestrowano komendy!§r.");
        registerListeners();
        getServer().getConsoleSender().sendMessage("§8[§a§l777-Backupy§8] §f§nZarejestrowano listenery!§r.");


        getServer().getConsoleSender().sendMessage("§8[§a§l777-Backupy§8] §aUruchomiono plugin!");
    }

    @Override
    public void onDisable() {
        if (databaseManager != null) {
            databaseManager.disconnect();
        }
        getServer().getConsoleSender().sendMessage("§8[§4§l777-Backupy§8] §cWylaczono plugin :C");
    }

    private void initializeConfigurations() {
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }

        configuration = new Configuration(this);
        configuration.loadConfiguration();

        messages = new Messages(this);
        messages.loadConfiguration();
    }

    private void initializeDatabase() {
        databaseManager = new DatabaseManager(this);
        databaseManager.initialize();
    }

    private void initializeManagers() {
        backupManager = new BackupManager(this);
        guiManager = new GuiManager(this);
        webhookManager = new WebhookManager(this);
    }

    private void registerCommands() {
        getCommand("nadajbackup").setExecutor(new BackupAdminCommand(this));
        getCommand("odbierzbackupa").setExecutor(new ClaimBackupCommand(this));
    }

    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new PlayerDeathListener(this), this);
    }

    public static Main getInstance() {
        return instance;
    }

    public Configuration getConfiguration() {
        return configuration;
    }

    public Messages getMessages() {
        return messages;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public BackupManager getBackupManager() {
        return backupManager;
    }

    public GuiManager getGuiManager() {
        return guiManager;
    }

    public WebhookManager getWebhookManager() {
        return webhookManager;
    }
}
