<config>
    <input>
        <jar in="target/777-Backupy-1.1.jar" out="target/777-Backupy-1.1-obf.jar"/>
    </input>

    <keep-names>
        <class access="public" name="me.darkness.backup.Main">
            <method access="public" name="onEnable"/>
            <method access="public" name="onDisable"/>
            <method access="public" name="onLoad"/>
        </class>

        <class access="public" name="me.darkness.backup.commands.admin.BackupAdminCommand">
            <method access="public" name="onCommand"/>
        </class>
        <class access="public" name="me.darkness.backup.commands.player.ClaimBackupCommand">
            <method access="public" name="onCommand"/>
        </class>

        <class access="public" name="me.darkness.backup.gui.GuiListener">
            <method access="public" name="onInventoryClick"/>
            <method access="public" name="onInventoryClose"/>
        </class>

        <class access="public" name="me.darkness.backup.listeners.PlayerDeathListener">
            <method access="public" name="onPlayerDeath"/>
        </class>
    </keep-names>
    
    <ignore-classes>
        <class template="class org.bukkit.**"/>
        <class template="class org.spigotmc.**"/>
        <class template="class net.md_5.**"/>
        <class template="class io.papermc.**"/>

        <class template="class com.mysql.**"/>
        <class template="class com.zaxxer.**"/>
        <class template="class com.mongodb.**"/>
        <class template="class org.bson.**"/>

        <class template="class org.yaml.**"/>
        <class template="class com.google.**"/>
        <class template="class org.slf4j.**"/>

        <class template="class java.**"/>
        <class template="class javax.**"/>
        <class template="class sun.**"/>
        <class template="class com.sun.**"/>
        <class template="class jdk.**"/>

        <class template="class lombok.**"/>
    </ignore-classes>

    <watermark key="777" value="777CODE"/>
    <property name="log-file" value="target/obfuscation.log"/>
    <property name="random-seed" value="maximum"/>
    <property name="string-encryption" value="enable"/>
    <property name="control-flow-obfuscation" value="enable"/>
    <property name="extensive-flow-obfuscation" value="maximum"/>
    <property name="packages-naming" value="123"/>
    <property name="methods-naming" value="iii"/>
    <property name="fields-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="classes-naming" value="iii"/>
    <property name="methods-naming" value="iii"/>
    <property name="local-variables-naming" value="optimize"/>
    <property name="line-numbers" value="obfuscate"/>
    <property name="generics" value="remove"/>
    <property name="inner-classes" value="remove"/>
    <property name="member-reorder" value="enable"/>
</config>
